<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shands.mod.dao.mapper.datarevision.AdsAppTradeRevenueDMapper">
  <resultMap type="com.shands.mod.dao.model.datarevision.po.AdsAppTradeRevenueD" id="AdsAppTradeRevenueDResultMap">
    <result property="id" column="id" jdbcType="INTEGER"/>
    <result property="hotelCode" column="hotel_code" jdbcType="VARCHAR"/>
    <result property="hotelName" column="hotel_name" jdbcType="VARCHAR"/>
    <result property="bizDepartmentCode" column="biz_department_code" jdbcType="VARCHAR"/>
    <result property="hotelBizDepartment" column="hotel_biz_department" jdbcType="VARCHAR"/>
    <result property="brandCode" column="biz_department_code" jdbcType="VARCHAR"/>
    <result property="hotelBrand" column="hotel_brand" jdbcType="VARCHAR"/>
    <result property="bizDate" column="biz_date" jdbcType="DATE"/>
    <result property="roomNum" column="room_num" jdbcType="INTEGER"/>
    <result property="memRoomsAmt" column="mem_rooms_amt" jdbcType="DOUBLE"/>
    <result property="memRoomNightsN" column="mem_room_nights_n" jdbcType="DOUBLE"/>
    <result property="roomNightsN" column="room_nights_n" jdbcType="DOUBLE"/>
  </resultMap>

  <sql id="baseColumn">
    id, hotel_code, hotel_name, biz_department_code, hotel_biz_department, biz_department_code, hotel_brand, biz_date, IFNULL(room_num, 0) as room_num, IFNULL(mem_rooms_amt, 0) as mem_rooms_amt, IFNULL(mem_room_nights_n, 0) as mem_room_nights_n, IFNULL(room_nights_n, 0) as room_nights_n
  </sql>

  <select id="complexSelect" resultMap="AdsAppTradeRevenueDResultMap">
    SELECT
    <include refid="baseColumn" />
    FROM ads_app_trade_revenue_d
    <where>
      <if test="startTime != null and startTime != ''">
        AND biz_date &gt;= #{startTime}
      </if>
      <if test="endTime != null and endTime != ''">
        AND biz_date &lt;= #{endTime}
      </if>
    </where>
  </select>


  <select id="selectRangeData" resultType="com.shands.mod.dao.model.statistics.dto.AdsAppTradeRevenueDDto">
    SELECT
    IFNULL(SUM(total_amt),0) as total_amt,
    IFNULL(SUM(total_room_amt),0) as total_room_amt,
    IFNULL(SUM(catering_amt),0) as catering_amt,
    IFNULL(SUM(other_amt),0) as other_amt,
    IFNULL(CONVERT((SUM(room_nights_n)/SUM(room_num)*100),DECIMAL(12,2)),0) as invest_occ,
    IFNULL(CONVERT((SUM(room_amt)/SUM(room_nights_n)),DECIMAL(12,2)),0) invest_adr,
    IFNULL(CONVERT((SUM(room_amt)/SUM(room_num)),DECIMAL(12,2)),0) invest_revpar,

    FROM ads_app_trade_revenue_d
    <where>
      <if test="startTime != null">
        AND biz_date &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        AND biz_date &lt;= #{endTime}
      </if>
      <if test="hotelCode != null and hotelCode != ''">
        AND hotel_code = #{hotelCode}
      </if>
    </where>
  </select>


  <select id="selectRangeDataList" resultType="com.shands.mod.dao.model.statistics.dto.AdsAppTradeRevenueDDto">
    SELECT
      IFNULL(total_amt,0) as total_amt,
      IFNULL(room_amt,0) as room_amt,
      IFNULL(catering_amt,0) as catering_amt,
      IFNULL(other_amt,0) as other_amt,
      IFNULL(CONVERT((room_nights_n/room_num*100),DECIMAL(12,2)),0) as invest_occ,
      IFNULL(CONVERT((room_amt/room_nights_n),DECIMAL(12,2)),0) invest_adr,
      IFNULL(CONVERT((room_amt/room_num),DECIMAL(12,2)),0) invest_revpar,
      biz_date AS bizDate
    FROM ads_app_trade_revenue_d
    <where>
      <if test="startTime != null">
        AND biz_date &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        AND biz_date &lt;= #{endTime}
      </if>
      <if test="hotelCode != null and hotelCode != ''">
        AND hotel_code = #{hotelCode}
      </if>
    </where>
  </select>

  <select id="queryNightAuditNum" resultMap="AdsAppTradeRevenueDResultMap">
    SELECT
    <include refid="baseColumn"/>
    FROM
      ads_app_trade_revenue_d
    WHERE
      biz_date = #{bizDate}
      AND hotel_code IN
      <foreach collection="hotelCodeList" item="hotelCode" open="(" separator="," close=")">
        #{hotelCode}
      </foreach>
      AND room_nights != 0
      AND room_nights IS NOT NULL
  </select>

</mapper>